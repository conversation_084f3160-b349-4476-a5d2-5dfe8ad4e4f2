import { getQivsPool } from './sqlConnectionPool.js';
import mssql from 'mssql';
import { User } from '../models/user.js';

const USER_QUERY = `
    SELECT u.id, u.name, u.nt_username, u.office_id, u.qualifications, u.email, u.target_role, u.h_target_role, r.role,
           o.name as office_name, o.phone_number, o.fax_number, o.address_line_1, o.address_line_2, o.address_line_3,
           o.address_line_4, o.address_line_5, o.post_code
    FROM monarch.[user] u
    LEFT JOIN monarch.office o ON o.id = u.office_id
    LEFT JOIN monarch.user_role ur ON ur.user_id = u.id
    LEFT JOIN monarch.role r ON r.id = ur.role_id
`;

export async function getUserById(id) {
  const pool = await getQivsPool();
  const query = `${USER_QUERY} WHERE u.id = @id`;

  try {
    const result = await pool.request().input('id', mssql.VarChar, id).query(query);
    if (result.recordset.length === 0) {
      return null;
    }
    return User.fromDatabaseRecordset(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-301', 'Error fetching user profile:', error);
    throw new Error('Failed to fetch user profile');
  }
}

export async function getUserByUsername(username) {
  const pool = await getQivsPool();
  const query = `${USER_QUERY} WHERE u.nt_username = @username`;
  try {
    const result = await pool.request().input('username', mssql.VarChar, username).query(query);
    if (result.recordset.length === 0) {
      return null;
    }

    return User.fromDatabaseRecordset(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-302', 'Error fetching user profile by username:', error);
    throw new Error('Failed to fetch user profile by username');
  }
}

export async function getUsersByRole(role) {
  const pool = await getQivsPool();
  const query = `
    SELECT u.id, u.name, u.nt_username, u.office_id, u.qualifications, u.email, u.target_role, u.h_target_role, r.role,
           o.name as office_name, o.phone_number, o.fax_number, o.address_line_1, o.address_line_2, o.address_line_3,
           o.address_line_4, o.address_line_5, o.post_code
    FROM monarch.[user] u
    LEFT JOIN monarch.office o ON o.id = u.office_id
    LEFT JOIN monarch.user_role ur ON ur.user_id = u.id
    LEFT JOIN monarch.role r ON r.id = ur.role_id
    WHERE r.role = @role
    ORDER BY u.name ASC
  `;

  try {
    const result = await pool.request()
      .input('role', mssql.VarChar, role)
      .query(query);

    return User.fromDatabaseRecordsets(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-303', 'Error fetching users by role:', error);
    throw new Error('Failed to fetch users by role');
  }
}


export async function getUsers() {
  const pool = await getQivsPool();
  const query = `${USER_QUERY} ORDER BY u.name ASC`;

  try {
    const result = await pool.request().query(query);
    return User.fromDatabaseRecordsets(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-304', 'Error fetching user profiles:', error);
    throw new Error('Failed to fetch user profiles');
  }
}

export async function getValuerUsers() {
  const pool = await getQivsPool();
  const query = `${USER_QUERY} WHERE r.role LIKE '%Valuer%' ORDER BY u.name ASC`;

  try {
    const result = await pool.request().query(query);
    return User.fromDatabaseRecordsets(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-305', 'Error fetching valuer profiles:', error);
    throw new Error('Failed to fetch valuer profiles');
  }
}

export async function getCountersignerUsers() {
  const pool = await getQivsPool();
  const query = `${USER_QUERY} WHERE r.role = 'Registered Valuer' ORDER BY u.name ASC`;

  try {
    const result = await pool.request().query(query);
    return User.fromDatabaseRecordsets(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-306', 'Error fetching countersigners profiles:', error);
    throw new Error('Failed to fetch countersigners profiles');
  }
}

export async function getRatingValuerUsers(options = {}) {
  const {
    isValuer = null,
    isRegisteredValuer = null,
    isActive = null,
    username = null,
    pageSize = 5000,
    offset = 0,
    orderBy = 'name',
    desc = false
  } = options;

  const pool = await getQivsPool();

  const direction = desc ? -1 : 1;
  const orderCol = { id: 1, name: 2 }[orderBy] || 0;

  const query = `
      WITH users AS (
          SELECT
              ROW_NUMBER() OVER (
            ORDER BY 
                CASE @orderCol
                    WHEN 1 THEN CAST(u.qv_user_id AS VARBINARY)
                    WHEN 2 THEN CAST(e.full_name AS VARBINARY)
                    ELSE NULL
                END
        ) AS row_nbr,
                  u.qv_user_id AS id,
              ISNULL(eg.is_valuer, 0) AS is_valuer,
              CASE
                  WHEN eg.code <> 3 AND eg.is_valuer = 1 THEN 1
                  ELSE 0
                  END AS is_registered_valuer,
              ISNULL(u.active, 0) AS active
          FROM qv_user u
                   LEFT JOIN employee e ON u.employee_id = e.employee_id
                   LEFT JOIN employee_group_type eg ON e.employee_group_id = eg.employee_group_id
                   LEFT JOIN qv_office o ON e.qv_office_id = o.qv_office_id
      )

      SELECT
          u.qv_user_id AS id,
          u.qv_user AS username,
          e.nt_user_name,
          e.full_name AS name,
          e.email_address,
          e.qualifications,
          e.code AS employee_code,
          u.last_login AS last_login_date,
          CASE
              WHEN u.active = 1 AND LEN(u.qv_user) > 0 THEN 'true'
              ELSE 'false'
              END AS is_active_yn,
          CASE WHEN u.locked_out = 1 THEN 'true' ELSE 'false' END AS is_locked_out_yn,
          CASE WHEN u.must_change_password = 1 THEN 'true' ELSE 'false' END AS must_change_password_yn,
          CASE WHEN u.cannot_change_password = 1 THEN 'true' ELSE 'false' END AS cannot_change_password_yn,
          u.failed_logins AS failed_logins_count,
          CASE WHEN p.is_valuer = 1 THEN 'true' ELSE 'false' END AS is_valuer_yn,
          CASE WHEN p.is_registered_valuer = 1 THEN 'true' ELSE 'false' END AS is_registered_valuer_yn,
          o.qv_office_id AS office_id,
          RTRIM(o.qv_office_code) AS office_code,
          RTRIM(o.qv_office_name) AS office_name,
          RTRIM(o.phone_number_day) AS office_phone_number,
          RTRIM(o.fax_number) AS office_fax_number,
          RTRIM(o.line_1_text) AS office_address_line_1,
          RTRIM(o.line_2_text) AS office_address_line_2,
          RTRIM(o.line_3_text) AS office_address_line_3,
          RTRIM(o.line_4_text) AS office_address_line_4,
          RTRIM(o.line_5_text) AS office_address_line_5,
          RTRIM(o.post_code) AS office_post_code

      FROM users p
               INNER JOIN qv_user u ON u.qv_user_id = p.id
               LEFT JOIN employee e ON u.employee_id = e.employee_id
               LEFT JOIN qv_office o ON e.qv_office_id = o.qv_office_id

      WHERE
          (p.is_valuer = @isValuer OR @isValuer IS NULL)
        AND (p.is_registered_valuer = @isRegisteredValuer OR @isRegisteredValuer IS NULL)
        AND (ISNULL(u.active, 0) = @isActive OR @isActive IS NULL)
        AND (u.qv_user = @username OR @username IS NULL)

      ORDER BY row_nbr * @direction
      OFFSET @offset ROWS
          FETCH NEXT @pageSize ROWS ONLY;
  `;

  try {
    const request = pool.request()
      .input('orderCol', mssql.Int, orderCol)
      .input('isValuer', mssql.Bit, isValuer)
      .input('isRegisteredValuer', mssql.Bit, isRegisteredValuer)
      .input('isActive', mssql.Bit, isActive)
      .input('username', mssql.VarChar, username)
      .input('desc', mssql.Bit, desc ? 1 : 0)
      .input('offset', mssql.Int, offset)
      .input('pageSize', mssql.Int, pageSize)
      .input('direction', mssql.Int, direction);

    const result = await request.query(query);

    if (result.recordset.length === 0) {
      return [];
    }

    const userResults = [];
    for (const row of result.recordset) {
      const user = User.fromSearchResultRow(row);
      userResults.push(user);
    }

    return userResults;
  } catch (error) {
    logger.error('ERR-USR-307', 'Error fetching rating valuers:', error);
    throw new Error(`Failed to search users: ${error.message}`);
  }
}

export async function saveUser(user) {
  const pool = await getQivsPool();

  try {
    const checkQuery = `
        SELECT 1 FROM monarch.[user] WHERE id = @id
    `;

    const insertQuery = `
      INSERT INTO monarch.[user] (id, name, nt_username, office_id, qualifications, email, target_role, h_target_role)
      VALUES (@id, @name, @nt_username, @office_id, @qualifications, @email, @target_role, @h_target_role)
    `;

    const updateQuery = `
    UPDATE monarch.[user]
    SET
      name = @name,
      nt_username = @nt_username,
      office_id = @office_id,
      qualifications = @qualifications,
      email = @email,
      target_role = @target_role,
      h_target_role = @h_target_role
    WHERE id = @id
  `;

    const request = await pool
      .request()
      .input('id', mssql.VarChar, user.id)
      .input('name', mssql.VarChar, user.name?.trim())
      .input('nt_username', mssql.VarChar, user.ntUsername?.trim())
      .input('office_id', mssql.VarChar, user.office?.id)
      .input('qualifications', mssql.VarChar, user.qualifications?.trim())
      .input('email', mssql.VarChar, user.email?.trim())
      .input('target_role', mssql.VarChar, user.target?.trim())
      .input('h_target_role', mssql.VarChar, user.prevTarget?.trim());
    const checkResult = await request.query(checkQuery);
    if (checkResult.recordset.length > 0) {
      await request.query(updateQuery);
    } else {
      await request.query(insertQuery);
    }

    await pool
      .request()
      .input('user_id', mssql.VarChar, user.id)
      .query('DELETE FROM monarch.user_role WHERE user_id = @user_id');

    if (user.roles?.length > 0) {
      const insertRoleQuery = `
        INSERT INTO monarch.user_role (user_id, role_id)
        SELECT @user_id, id FROM monarch.role WHERE role = @role
      `;

      for (const role of user.roles) {
        await pool
          .request()
          .input('user_id', mssql.VarChar, user.id)
          .input('role', mssql.VarChar, role)
          .query(insertRoleQuery);
      }
    }

    const result = await pool
      .request()
      .input('id', mssql.VarChar, user.id)
      .query(`${USER_QUERY} WHERE u.id = @id`);

    if (result.recordset.length === 0) {
      return null;
    }

    return User.fromDatabaseRecordset(result.recordset);
  } catch (error) {
    logger.error('ERR-USR-308', 'Error saving user:', error);
    throw new Error('Failed to save user');
  }
}

export async function validateUserName(name, id) {
  const pool = await getQivsPool();
  try {
    const result = await pool
      .request()
      .input('name', mssql.VarChar, name)
      .input('id', mssql.VarChar, id)
      .query('SELECT 1 FROM monarch.[user] WHERE name = @name AND id != @id');
    return result.recordset.length > 0;
  } catch (error) {
    logger.error('ERR-USR-309', 'Error validating user name:', error);
    throw new Error('Failed to validate user name');
  }
}

export async function validateUserUsername(username, id) {
  const pool = await getQivsPool();
  try {
    const result = await pool
      .request()
      .input('nt_username', mssql.VarChar, username)
      .input('id', mssql.VarChar, id)
      .query('SELECT 1 FROM monarch.[user] WHERE nt_username = @nt_username AND id != @id');
    return result.recordset.length > 0;
  } catch (error) {
    logger.error('ERR-USR-310', 'Error validating user username:', error);
    throw new Error('Failed to validate user username');
  }
}