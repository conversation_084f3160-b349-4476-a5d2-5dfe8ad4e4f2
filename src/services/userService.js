import { validate as uuidValidate } from 'uuid';
import { v4 as uuidv4 } from 'uuid';

import { STATUS_INVALID, STATUS_MISSING, STATUS_NOT_FOUND } from '../enums/crudStatus.js';
import * as userDal from '../dal/userDal.js';
import { validateUser } from '../validation/userValidation.js';

export async function getUserById(id) {
  if (!uuidValidate(id)) {
    return {
      status: STATUS_INVALID,
      message: 'Invalid User ID format'
    };
  }

  const user = await userDal.getUserById(id);
  if (!user) {
    return {
      status: STATUS_NOT_FOUND,
      message: 'User Not Found'
    };
  }

  return user;
}

export async function getUserByUsername(username) {
  logger.info(`getUserByUsername ${username}`);
  if(!username) {
    return {
      status: STATUS_MISSING,
      message: 'Username must be provided'
    };
  }

  const user = await userDal.getUserByUsername(username);
  if (!user) {
    return {
      status: STATUS_NOT_FOUND,
      message: 'User Not Found'
    };
  }

  return user;
}

export async function getUsersByRole(role) {
  logger.info(`getUsersByRole ${role}`);
  return userDal.getUsersByRole(role);
}

export async function getUsers() {
  return userDal.getUsers();
}

export async function getValuers() {
  return userDal.getValuerUsers();
}

export async function getCountersigners() {
  return userDal.getCountersignerUsers();
}

export async function getRatingValuers() {
  return userDal.getRatingValuerUsers({
    isValuer: true,
    isActive: true,
    pageSize: 5000,
    offset: 0,
    orderBy: 'name',
    desc: false
  });
}

export async function saveUser(user) {
  logger.info(`saveUser`,{ user });
  if (!user) {
    return {
      status: STATUS_INVALID,
      message: 'a valid payload must be provided'
    };
  }

  user.id = user.id || uuidv4();
  const errors = await validateUser(user);
  if (errors.length > 0) {
    return {
      status: STATUS_INVALID,
      errors
    };
  }

  return await userDal.saveUser(user)
}
