import { expect } from 'chai';
import { getRatingValuerUsers } from '../../src/dal/userDal.js';
import { testIds } from './consts.js';
import './setup.js';

describe('getRatingValuerUsers', () => {
  it('should return all active valuers when no filters applied', async () => {
    const users = await getRatingValuerUsers();
    expect(users).to.be.an('array');
    expect(users.length).to.be.at.least(2);
    
    const activeUsers = users.filter(u => u.externalId === 'johnvaluer' || u.externalId === 'janesenior');
    expect(activeUsers.length).to.equal(2);
  });

  it('should filter by isValuer=true', async () => {
    const users = await getRatingValuerUsers({ isValuer: true });
    expect(users).to.be.an('array');
    expect(users.length).to.be.at.least(2);
    
    users.forEach(user => {
      expect(user.roles).to.include.oneOf(['Valuer', 'Senior Valuer']);
    });
  });

  it('should filter by isRegisteredValuer=true', async () => {
    const users = await getRatingValuerUsers({ isRegisteredValuer: true });
    expect(users).to.be.an('array');
    expect(users.length).to.be.at.least(1);
    
    const seniorValuer = users.find(u => u.externalId === 'janesenior');
    expect(seniorValuer).to.not.be.undefined;
    expect(seniorValuer.roles).to.include('Senior Valuer');
  });

  it('should filter by isActive=true', async () => {
    const users = await getRatingValuerUsers({ isActive: true });
    expect(users).to.be.an('array');
    expect(users.length).to.equal(2);
    
    const inactiveUser = users.find(u => u.externalId === 'bobadmin');
    expect(inactiveUser).to.be.undefined;
  });

  it('should filter by username', async () => {
    const users = await getRatingValuerUsers({ username: 'johnvaluer' });
    expect(users).to.be.an('array');
    expect(users.length).to.equal(1);
    expect(users[0].externalId).to.equal('johnvaluer');
    expect(users[0].name).to.equal('John Valuer');
  });

  it('should support pagination with pageSize and offset', async () => {
    const firstPage = await getRatingValuerUsers({ pageSize: 1, offset: 0 });
    expect(firstPage).to.be.an('array');
    expect(firstPage.length).to.equal(1);
    
    const secondPage = await getRatingValuerUsers({ pageSize: 1, offset: 1 });
    expect(secondPage).to.be.an('array');
    expect(secondPage.length).to.equal(1);
    
    expect(firstPage[0].externalId).to.not.equal(secondPage[0].externalId);
  });

  it('should support ordering by name', async () => {
    const usersAsc = await getRatingValuerUsers({ orderBy: 'name', desc: false });
    expect(usersAsc).to.be.an('array');
    expect(usersAsc.length).to.be.at.least(2);
    
    const usersDesc = await getRatingValuerUsers({ orderBy: 'name', desc: true });
    expect(usersDesc).to.be.an('array');
    expect(usersDesc.length).to.be.at.least(2);
    
    expect(usersAsc[0].name).to.not.equal(usersDesc[0].name);
  });

  it('should return users with correct structure', async () => {
    const users = await getRatingValuerUsers({ pageSize: 1 });
    expect(users).to.be.an('array');
    expect(users.length).to.equal(1);
    
    const user = users[0];
    expect(user).to.have.property('externalId');
    expect(user).to.have.property('name');
    expect(user).to.have.property('ntUsername');
    expect(user).to.have.property('email');
    expect(user).to.have.property('qualifications');
    expect(user).to.have.property('roles');
    expect(user).to.have.property('office');
    expect(user.roles).to.be.an('array');
  });

  it('should return empty array when no matches found', async () => {
    const users = await getRatingValuerUsers({ username: 'nonexistent' });
    expect(users).to.be.an('array');
    expect(users.length).to.equal(0);
  });
});
