import * as service from './endpoints.js';
import { validateUserArray } from '../common/validators.js';import { expect } from "chai";

let result = null;

describe('get rating valuers', () => {
  before(async () => {
    result = await service.getRatingValuers();
  });

  it('responds 200 OK', () => {
    expect(result).to.have.status(200);
  });

  it('returns valid users array', () => {
    validateUserArray(result.body);
  });

  it('returns only valuers and senior valuers', () => {
    result.body.forEach((user) => {
      expect(user.roles).to.include.oneOf(['Valuer', 'Senior Valuer']);
    });
  });
});
