version: 0.2
env:
  shell: bash
  exported-variables:
    - COMMIT_EMAIL
    - COMMIT_SHA
    - SLACK_USER
    - BUILD_VERSION
    - BUILD_BRANCH
    - RELATIVE_REPORT_PATH
phases:
  install:
    runtime-versions:
      java: corretto8
  pre_build:
    commands:
      - aws s3 cp s3://qv-deployment/build-scripts/project_buildspec_env_vars.sh env_vars.sh && chmod +x env_vars.sh && . ./env_vars.sh
      - '[ -n "$BUILD_VERSION" ] || (echo "BUILD_VERSION IS NOT SET, EXITING..." && false)'
      - export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain quotable-value --domain-owner 948396734470 --region ap-southeast-2 --query authorizationToken --output text`

  build:
    commands:
      - ls -al
      - cd sdk
      - ./gradlew build

      - echo "uploading unit test report started"
      - aws s3 cp s3://qv-deployment/build-scripts/create_test_report.sh create_test_report.sh && chmod +x create_test_report.sh
      - . ./create_test_report.sh "$PROJECT_NAME" "build" "unit" "$BUILD_BRANCH" "$BUILD_VERSION" "$COMMIT_SHA"
      - if [ -n "$REPORT_FOLDER" ]; then echo "REPORT_FOLDER is set and not empty"; else echo "REPORT_FOLDER is unset or empty"; exit 1; fi
      - aws s3 cp build/reports/tests/test/ s3://$REPORT_FOLDER --recursive || true
      - echo "uploading unit test report completed"

      - echo "Publishing artifacts"
      - ./gradlew publish
      - echo "Publishing artifacts completed"

cache:
  paths:
    - /root/.gradle
    - .gradle
