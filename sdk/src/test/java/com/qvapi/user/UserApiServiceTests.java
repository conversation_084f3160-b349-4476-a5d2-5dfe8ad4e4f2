package com.qvapi.user;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.qv.common.service.exceptions.LambdaException;
import com.qv.common.service.util.FileUtils;
import com.qv.common.service.util.LambdaClient;
import com.qv.common.service.util.LocalLambdaClient;
import com.qvapi.user.model.Office;
import com.qvapi.user.model.User;
import com.qvapi.user.util.Payload;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

@ExtendWith(MockitoExtension.class)
public class UserApiServiceTests {
    private static LambdaClient mockClient;
    private static UserApiService service;
    private static ObjectMapper objectMapper;
    private static List<User> testUsers;
    private static Office testOffice;

    @BeforeAll
    static void setUpAll() throws IOException {
        objectMapper = new ObjectMapper();
        String usersJson = FileUtils.readToString("fixtures/users.json");
        String officeJson = FileUtils.readToString("fixtures/office.json");
        testUsers = Arrays.asList(objectMapper.readValue(usersJson, User[].class));
        testOffice = objectMapper.readValue(officeJson, Office.class);
    }

    @BeforeEach
    void setUp() {
        mockClient = Mockito.spy(new LocalLambdaClient(UserApi.SERVICE_NAME));
        service = new UserApiService("local", mockClient);
        Mockito.reset(mockClient);
    }

    @Test
    public void getWithValidId() throws IOException {
        User testUser = testUsers.get(0);
        String stubResponse = Payload.formatLambdaResponse(200, testUser);
        doReturn(stubResponse)
                .when(mockClient)
                .invoke(
                        eq(UserApi.SERVICE_NAME),
                        eq(UserApi.Functions.getUser.getName()),
                        eq("{\"pathParameters\":{\"id\":\"550e8400-e29b-41d4-a716-************\"}}"),
                        eq("local")
                );

        User user = service.getUser(UUID.fromString("550e8400-e29b-41d4-a716-************"));
        assertThat(user).isEqualTo(testUser);
    }

    @Test
    public void getWithNonExistentId() throws IOException {
        String stubResponse = Payload.formatLambdaResponse(404, ImmutableMap.of(
                "status", "NOT_FOUND",
                "message", "User Not Found"
        ));

        doReturn(stubResponse)
                .when(mockClient)
                .invoke(
                        eq(UserApi.SERVICE_NAME),
                        eq(UserApi.Functions.getUser.getName()),
                        eq("{\"pathParameters\":{\"id\":\"00000000-0000-0000-0000-000000000000\"}}"),
                        eq("local")
                );

        assertThatThrownBy(() -> service.getUser(UUID.fromString("00000000-0000-0000-0000-000000000000"))).isInstanceOf(LambdaException.class).satisfies(exception -> {
            LambdaException lambdaException = (LambdaException) exception;
            assertThat(lambdaException.getCode()).isEqualTo(404);
            assertThat(lambdaException.getMessage()).isEqualTo("User Not Found");
        });
    }

    @Test
    public void getUserByUsername() throws JsonProcessingException {
        User testUser = testUsers.get(0);
        String stubResponse = Payload.formatLambdaResponse(200, testUser);
        doReturn(stubResponse)
                .when(mockClient)
                .invoke(
                        eq(UserApi.SERVICE_NAME),
                        eq(UserApi.Functions.getUserByUsername.getName()),
                        eq("{\"queryStringParameters\":{\"username\":\"QVNZ\\\\johndoe\"}}"),
                        eq("local")
                );

        User result = service.getUserByUsername("QVNZ\\johndoe");
        assertThat(result).isEqualTo(testUser);
    }

    @Test
    public void getUserByUsernameNotFound() {
        doThrow(new RuntimeException("User not found"))
                .when(mockClient)
                .invokeLambda(
                        eq(UserApi.Functions.getUserByUsername.getName()),
                        any(),
                        any(),
                        any(),
                        eq(UserApi.ResponseType.User)
                );

        assertThatThrownBy(() -> service.getUserByUsername("nonexistent"))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("User not found");
    }

    @Test
    public void saveUser() {
        User testUser = testUsers.get(0);
        doReturn(testUser)
                .when(mockClient)
                .invokeLambda(
                        eq(UserApi.Functions.saveUser.getName()),
                        any(),
                        any(),
                        any(),
                        eq(UserApi.ResponseType.User)
                );

        User result = service.saveUser(testUser);
        assertThat(result).isEqualTo(testUser);
    }

    @Test
    public void getUsers() {
        doReturn(testUsers)
                .when(mockClient)
                .invokeLambda(
                        eq(UserApi.Functions.getUsers.getName()),
                        any(),
                        any(),
                        any(),
                        eq(UserApi.ResponseType.UserList)
                );

        List<User> result = service.getUsers();
        assertThat(result).isEqualTo(testUsers);
    }

    @Test
    public void getValuers() {
        List<User> valuers = testUsers.stream().
                filter(User::isValuer)
                .collect(Collectors.toList());
        doReturn(valuers)
                .when(mockClient)
                .invokeLambda(
                        eq(UserApi.Functions.getValuers.getName()),
                        any(),
                        any(),
                        any(),
                        eq(UserApi.ResponseType.UserList)
                );

        List<User> result = service.getValuers();
        assertThat(result).isEqualTo(valuers);
    }

    @Test
    public void getRatingValuers() {
        List<User> ratingValuers = testUsers.stream()
                .filter(user -> user.isValuer() && user.getCanValueRatingUnit())
                .collect(Collectors.toList());
        doReturn(ratingValuers)
                .when(mockClient)
                .invokeLambda(
                        eq(UserApi.Functions.getRatingValuers.getName()),
                        any(),
                        any(),
                        any(),
                        eq(UserApi.ResponseType.UserList)
                );

        List<User> result = service.getRatingValuers();
        System.out.println(result);
        assertThat(result).isEqualTo(ratingValuers);
    }

    @Test
    public void getCountersigners() {
        List<User> countersigners = testUsers.stream()
                .filter(user -> user.getRoles().contains("Senior Valuer"))
                .collect(Collectors.toList());
        doReturn(countersigners)
                .when(mockClient)
                .invokeLambda(
                        eq(UserApi.Functions.getCountersigners.getName()),
                        any(),
                        any(),
                        any(),
                        eq(UserApi.ResponseType.UserList)
                );

        List<User> result = service.getCountersigners();
        assertThat(result).isEqualTo(countersigners);
    }

    @Test
    public void getRoleUsers() throws JsonProcessingException {
        List<User> roleUsers = testUsers.stream()
                .filter(user -> user.getRoles().contains("Valuer"))
                .collect(Collectors.toList());

        String stubResponse = Payload.formatLambdaResponse(200, roleUsers);
        doReturn(stubResponse)
                .when(mockClient)
                .invoke(
                        eq(UserApi.SERVICE_NAME),
                        eq(UserApi.Functions.getRoleUsers.getName()),
                        eq("{\"pathParameters\":{\"role\":\"Valuer\"}}"),
                        eq("local")
                );

        List<User> result = service.getRoleUsers("Valuer");
        assertThat(result).isEqualTo(roleUsers);
    }

    @Test
    public void getOffices() {
        List<Office> offices = Lists.newArrayList(testOffice);
        doReturn(offices)
                .when(mockClient)
                .invokeLambda(
                        eq(UserApi.Functions.getOffices.getName()),
                        eq(null),
                        eq(null),
                        eq(null),
                        eq(UserApi.ResponseType.OfficeList)
                );
        List<Office> result = service.getOffices();
        assertThat(result).isEqualTo(offices);

    }

    @Test
    public void getRoles() {
        List<String> roles = Lists.newArrayList("Valuer", "Senior Valuer", "Admin");
        doReturn(roles)
                .when(mockClient)
                .invokeLambda(
                        eq(UserApi.Functions.getRoles.getName()),
                        eq(null),
                        eq(null),
                        eq(null),
                        eq(UserApi.ResponseType.RoleList)
                );

        List<String> result = service.getRoles();
        assertThat(result).isEqualTo(roles);
    }
}