package com.qvapi.user.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.qv.common.service.util.FileUtils;
import com.qvapi.user.model.Office;

import java.io.IOException;
import java.util.Map;
import java.util.UUID;

public class Payload {
    private static final ObjectMapper mapper = Mapper.createObjectMapper();

    public static String formatLambdaResponse(int statusCode, Object payload) throws JsonProcessingException {
        String sanitized = mapper.writeValueAsString(payload).replace("\"","\\\"").replaceAll("\r|\n","")
                .replace("\\\\","\\\\\\\\");
        return String.format("{\"statusCode\":%d,\"body\":\"%s\"}", statusCode, sanitized);
    }

    public static String formatLambdaRequest(Object payload, Map<String, String> queryParams) throws JsonProcessingException {
        Map<String, Object> payloadMap = Maps.newHashMap();

        if (queryParams != null && !queryParams.isEmpty()) {
            payloadMap.put("queryStringParameters", queryParams);
        }

        payloadMap.put("body", mapper.writeValueAsString(payload));
        return mapper.writeValueAsString(payloadMap);
    }

    public static <T> T parseFile(String filePath, Class<T> classType) throws IOException {
        String rawJson = FileUtils.readToString(filePath);
        System.out.println(rawJson);
        try {
            return mapper.readValue(rawJson, classType);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}
