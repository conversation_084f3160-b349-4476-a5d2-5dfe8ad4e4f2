package com.qvapi.user.model;

import lombok.Value;
import lombok.With;

import java.util.List;
import java.util.UUID;

@With
@Value
public class User {
    UUID id;
    String externalId;
    String name;
    String ntUsername;
    Office office;
    String qualifications;
    String email;
    String target;
    List<String> roles;
    String prevTarget;

    public Boolean isRegisteredValuer() {
        return roles != null && (roles.contains("Senior Valuer") || roles.contains("Managing Senior Valuer") || roles.contains("Area Valuer"));
    }

    public Boolean isValuer() {
        return roles != null && (roles.contains("Valuer") || isRegisteredValuer());
    }

    public Boolean isAdmin() {
        return roles != null && roles.contains("Receptionist Typist");
    }

    public Boolean getCanValueRatingUnit() {
        return roles != null && roles.contains("Rating Valuer");
    }
}