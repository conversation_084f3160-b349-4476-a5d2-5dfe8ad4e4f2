package com.qvapi.user;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.qv.common.service.BaseService;
import com.qv.common.service.util.LambdaClient;
import com.qv.common.service.util.LocalLambdaClient;
import com.qvapi.user.model.Office;
import com.qvapi.user.model.User;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class UserApiService extends BaseService implements UserApi {
    protected final Config config;
    protected final LambdaClient lambdaClient;
    protected final String env;
    protected final Boolean useLocal;
    protected final Integer port;
    private final ObjectMapper objectMapper;

    public UserApiService(String env, LambdaClient client) {
        super(env, client);
        this.lambdaClient = client;
        this.env = env;
        this.useLocal = false;
        this.port = 0;
        this.config = null;
        this.objectMapper = new ObjectMapper();
    }

    public UserApiService(String env, Config config) {
        super(env, null);
        Config defaultConfig = ConfigFactory.parseMap(DEFAULT_PROPERTIES);

        if (config.hasPath(CONFIG_PATH)) {
            this.config = config.getConfig(CONFIG_PATH).withFallback(defaultConfig);
        } else {
            this.config = defaultConfig;
        }

        this.useLocal = this.config.getBoolean("useLocal");
        this.port = this.config.getInt("port");
        this.env = env;

        lambdaClient = useLocal ? new LocalLambdaClient(SERVICE_NAME, port)
                : new LambdaClient(env, SERVICE_NAME);
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public User getUser(UUID id) {
        ImmutableMap<String, Object> pathParameters = ImmutableMap.of("id", id);
        return lambdaClient.invokeLambda(UserApi.Functions.getUser.getName(), pathParameters, ImmutableMap.of(), null, ResponseType.User);
    }

    @Override
    public User getUserByUsername(String username) {
        Map<String, String> query = ImmutableMap.of("username", username);
        return lambdaClient.invokeLambda(Functions.getUserByUsername.getName(), null, query, null, ResponseType.User);
    }

    @Override
    public User saveUser(User user) {
        return lambdaClient.invokeLambda(Functions.saveUser.getName(), null, null, user, ResponseType.User);
    }

    @Override
    public List<User> getUsers() {
        return lambdaClient.invokeLambda(Functions.getUsers.getName(), null, null, null, ResponseType.UserList);
    }

    @Override
    public List<User> getValuers() {
        return lambdaClient.invokeLambda(Functions.getValuers.getName(), null, null, null, ResponseType.UserList);
    }

    @Override
    public List<User> getRatingValuers() {
        return lambdaClient.invokeLambda(Functions.getRatingValuers.getName(), null, null, null, ResponseType.UserList);
    }

    @Override
    public List<User> getCountersigners() {
        return lambdaClient.invokeLambda(Functions.getCountersigners.getName(), null, null, null, ResponseType.UserList);
    }

    @Override
    public List<User> getRoleUsers(String role) {
        ImmutableMap<String, Object> pathParameters = ImmutableMap.of("role", role);
        return lambdaClient.invokeLambda(Functions.getRoleUsers.getName(), pathParameters, null, null, ResponseType.UserList);
    }

    @Override
    public List<Office> getOffices() {
        return lambdaClient.invokeLambda(Functions.getOffices.getName(), null, null, null, ResponseType.OfficeList);
    }

    @Override
    public List<String> getRoles() {
        return lambdaClient.invokeLambda(Functions.getRoles.getName(), null, null, null, ResponseType.RoleList);
    }
}
