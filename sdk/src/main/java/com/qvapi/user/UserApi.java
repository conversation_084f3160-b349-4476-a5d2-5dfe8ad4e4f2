package com.qvapi.user;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.qvapi.user.model.*;
import lombok.Getter;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface UserApi {
    String SERVICE_NAME = "api-user";
    String CONFIG_PATH = "qv.api.user";
    Map<String, ?> DEFAULT_PROPERTIES = Collections.unmodifiableMap(ImmutableMap.of(
            "useLocal", false,
            "port", 3002
    ));

    @Getter
    enum Functions {
        getUser("getUser"),
        getUserByUsername("getUserByUsername"),
        saveUser("saveUser"),
        getUsers("getUsers"),
        getValuers("getValuers"),
        getRatingValuers("getRatingValuers"),
        getCountersigners("getCountersigners"),
        getOffices("getOffices"),
        getRoles("getRoles"),
        getRoleUsers("getRoleUsers");

        private final String name;

        Functions(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    class ResponseType {
        public static final TypeReference<User> User = new TypeReference<User>() {};
        public static final TypeReference<List<User>> UserList = new TypeReference<List<User>>() {};
        public static final TypeReference<List<String>> RoleList = new TypeReference<List<String>>() {};
        public static final TypeReference<List<Office>> OfficeList = new TypeReference<List<Office>>() {};
    }

    User getUser(UUID id);
    User getUserByUsername(String username);
    User saveUser(User user);
    List<User> getUsers();
    List<User> getValuers();
    List<User> getRatingValuers();
    List<User> getCountersigners();
    List<User> getRoleUsers(String role);
    List<Office> getOffices();
    List<String> getRoles();
}
