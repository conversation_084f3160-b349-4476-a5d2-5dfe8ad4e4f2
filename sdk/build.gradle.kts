import java.io.ByteArrayOutputStream

plugins {
    java
    `maven-publish`
}

group = "com.qvapi"

fun getCodeArtifactAuthToken(): String {
    return System.getenv("CODEARTIFACT_AUTH_TOKEN") ?: ""
}

fun getCurrentRevision(): String {
    val outputStream = ByteArrayOutputStream()
    exec {
        commandLine = "git describe --tags".split(" ")
        standardOutput = outputStream
    }
    return outputStream.toString().trim()
}


val gitRevision: String = try {
    getCurrentRevision()
} catch (e: Exception) {
    "local" // Fallback in case of error
}
val gitTag: String = System.getenv("CODEBUILD_GIT_TAG") ?: ""
version = gitTag
if (version == "") {
    version = "${gitRevision}-SNAPSHOT"
}

println("Building property api: v$version")

repositories {
    repositories {
        mavenLocal()
        mavenCentral()
        maven {
            name = "awsCodeArtifact"
            url =
                uri("https://quotable-value-948396734470.d.codeartifact.ap-southeast-2.amazonaws.com/maven/monarch-repository/")
            credentials {
                val awsCredentials =
                    "aws codeartifact get-authorization-token --domain quotable-value --domain-owner 948396734470 --query authorizationToken --output text"
                val authToken = Runtime.getRuntime().exec(awsCredentials).inputStream.reader().readText().trim()
                username = "aws"
                password = authToken
            }
        }
    }
}

dependencies {
    val junitVersion = "5.9.2"
    val lombokVersion = "1.18.32"
    val jacksonVersion = "2.17.0"
    val awsSdkVersion = "1.12.701"
    val monarchCommonVersion = "1.0.0-16-gf196657-SNAPSHOT"

    implementation("com.amazonaws:aws-java-sdk-lambda:$awsSdkVersion")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jacksonVersion")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jdk8:$jacksonVersion")
    implementation("com.qv:monarch-common:$monarchCommonVersion")
    implementation("com.typesafe:config:1.3.3")

    // Test dependencies
    testImplementation(platform("org.junit:junit-bom:$junitVersion"))
    testImplementation("org.junit.jupiter:junit-jupiter")
    testImplementation("org.assertj:assertj-core:3.22.0")
    testImplementation("org.mockito:mockito-core:4.5.1")
    testImplementation("org.mockito:mockito-junit-jupiter:4.5.1")
    testImplementation("com.google.guava:guava:22.0")
    testImplementation("ch.qos.logback:logback-classic:1.2.3")

    // Compile only dependencies
    compileOnly("org.projectlombok:lombok:$lombokVersion")
    annotationProcessor("org.projectlombok:lombok:$lombokVersion")

    // Compile only dependencies, they are included in monarch-web
    compileOnly("com.google.inject:guice:4.1.0")
    compileOnly("com.google.guava:guava:22.0")
    compileOnly("org.slf4j:slf4j-api:1.7.25")
}

tasks.test {
    useJUnitPlatform()
}

val sourcesJar by tasks.registering(Jar::class) {
    archiveClassifier.set("sources")
    from(sourceSets.main.get().allSource)
}

publishing {
    publications {
        create<MavenPublication>("mavenJava") {
            from(components["java"])
            artifact(sourcesJar)
        }
    }
    repositories {
        maven {
            name = "awsCodeArtifact"
            url =
                uri("https://quotable-value-948396734470.d.codeartifact.ap-southeast-2.amazonaws.com/maven/monarch-repository/")
            credentials {
                val awsCredentials =
                    "aws codeartifact get-authorization-token --domain quotable-value --domain-owner 948396734470 --query authorizationToken --output text"
                val authToken = Runtime.getRuntime().exec(awsCredentials).inputStream.reader().readText().trim()
                username = "aws"
                password = authToken
            }
        }
    }
}


tasks.register("version") {
    group = "build"
    description = "Prints the current build version"
    doLast {
        println(version)
    }
}
